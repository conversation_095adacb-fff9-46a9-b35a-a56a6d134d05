import { BrowserRouter, Routes, Route } from 'react-router-dom'
import { SignInRoute } from './pages/SignInRoute'
import { Navbar } from './components/navbar'
import { SignUp } from './pages/SignUp'
import  JobList  from './pages/JobList'
import ApplicationForm from './pages/ApplicationForm'
import ProtectedRoute from './components/ProtectedRoute'
import ApplicationHistory from './pages/ApplicationHistory'
import { SuccessSignUp } from './pages/SuccessSignUp'
import RouteNotFound from './pages/404notFound'
import AckPage from './pages/AckPage'



function App() {
  return (
    <BrowserRouter>
    <Navbar/>
      <Routes>  
        <Route path='/' element={<SignInRoute/>}  />
        <Route path='/signup' element={<SignUp/>} />
        <Route path='/joblisting' element={<ProtectedRoute><JobList/></ProtectedRoute>} />
        <Route path='/applicationform' element={<ProtectedRoute><ApplicationForm/></ProtectedRoute>} /> 
        <Route path='/applicationhistory' element={<ProtectedRoute><ApplicationHistory/></ProtectedRoute>} />
        <Route path='/successsignup' element={ <SuccessSignUp/>} />
         <Route path='/ack' element={<ProtectedRoute><AckPage /></ProtectedRoute>} />
        <Route path='*' element={<RouteNotFound/>} />
      </Routes>
      
    </BrowserRouter>
  )
}

export default App
