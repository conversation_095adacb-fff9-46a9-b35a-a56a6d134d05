import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface ApplicationState {
    applicationId: string | null
    setApplicationId: (id: string) => void
    clearApplicationId: () => void
}

export const useApplicationStore = create<ApplicationState>()(
    persist(
        (set) => ({
            applicationId: null,
            setApplicationId: (id) => set({ applicationId: id }),
            clearApplicationId: () => set({ applicationId: null })
        }),
        {
            name: 'application-storage'
        }
    )
) 