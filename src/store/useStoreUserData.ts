import { create } from "zustand";

type User = {
  user_application_id: string;
  password: string;
  first_name: string;
  last_name: string;
  email: string;
  phoneNumber: string;
  gender: string;
  Qualification: string;
  address: string;
  city: string;
  state: string;
  country: string;
  pincode: string;
  role: string;
  status: string;
};

type UserData = {
  accessToken: string;
  user: User | null;

};

type UserDataStore = {
  userData: UserData | null;
  setUserData: (data: UserData) => void;
  clearUserData: () => void;
};

export const useStoreUserData = create<UserDataStore>((set) => ({
    
  userData: null,
  setUserData: (data) => set({ userData: data }),
  clearUserData: () => set({ userData: null }),
}));
