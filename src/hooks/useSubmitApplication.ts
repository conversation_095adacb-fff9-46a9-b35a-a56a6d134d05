import { useMutation } from '@tanstack/react-query';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

export function useSubmitApplication() {
  const navigate = useNavigate();
  return useMutation({
    mutationFn: async (applicationId: string) => {
      const url = `${import.meta.env.VITE_API_URL}/api/application/submit-application/${applicationId}`;
      const response = await axios.put(url, {}, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });
      return response.data;
    },
    onSuccess: () => {
      toast.success('Application has been submitted!');
      navigate('/joblisting');
    },
    onError: () => {
      toast.error('Failed to submit application.');
    },
  });
} 