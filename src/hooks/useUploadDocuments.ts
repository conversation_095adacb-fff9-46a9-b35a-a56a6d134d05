import { useMutation } from '@tanstack/react-query';
import axios from 'axios';

export function useUploadDocuments() {
  return useMutation({
    mutationFn: async ({
      applicationId,
      files,
    }: {
      applicationId: string;
      files: Record<string, File | null>;
    }) => {
      const formData = new FormData();
      Object.entries(files).forEach(([key, file]) => {
        if (file) formData.append(key, file);
      });

      const url = `${import.meta.env.VITE_API_URL || ''}/api/application/upload-documents/${applicationId}`;
      const response = await axios.post(url, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      return response.data;
    },
  });
} 