import { useMutation } from '@tanstack/react-query'
import axios from 'axios'
import { toast } from 'sonner'

interface ApplicationData {
    user_id: number
    job_id: number
}

export const useApplication = () => {
    const submitApplication = async (data: ApplicationData) => {
        const response = await axios.post(`${import.meta.env.VITE_API_URL}/api/application/create-application`, data, {
            headers: {
                'Content-Type': 'application/json',
            },
        })
        return response.data
    }

    return useMutation({
        mutationFn: submitApplication,
        onSuccess: () => {
            toast.success('Application submitted successfully!')
        },
        onError: (error) => {
            toast.error('Failed to submit 0 ')
            console.error('Application error:', error)
        },
    })
} 