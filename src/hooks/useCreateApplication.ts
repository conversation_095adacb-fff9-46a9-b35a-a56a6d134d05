import { useMutation } from '@tanstack/react-query'
import axios from 'axios'
import { useApplicationStore } from '@/store/applicationStore'

interface CreateApplicationData {
    user_id: number
    job_id: number
}

interface CreateApplicationResponse {
    success: boolean
    message: string
    application_id:number
    application :any

}

const createApplication = async (data: CreateApplicationData): Promise<CreateApplicationResponse> => {
    const response = await axios.post(
        `${import.meta.env.VITE_API_URL}/api/application/create-application`,
        data,
        {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        }
    )
    return response.data
}

export const useCreateApplication = () => {
    const setApplicationId = useApplicationStore((state) => state.setApplicationId)

    return useMutation({
        mutationFn: createApplication,
        onSuccess: (data) => {
            //set the Id in zustand .... 
            if (data) {
                setApplicationId(data.application.application_id)
                // localStorage.setItem("applicationId" ,JSON.stringify(data.application.application_id))
            }

            console.log("....",data.application.application_id)
        },
        onError: (error) => {
            console.error('Error submitting application:', error)
        }
    })
} 