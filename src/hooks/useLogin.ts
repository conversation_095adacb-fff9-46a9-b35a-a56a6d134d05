import { useMutation } from "@tanstack/react-query";
import axios from "axios";
import type { User } from "@/types/user";

type LoginCredentials = {
  user_application_id: string;
  password: string;
};

type LoginResponse = {
  accessToken: string;
  user: User;
  user_application_id : any 
};

async function loginUser(credentials: LoginCredentials): Promise<LoginResponse> {
  const response = await axios.post(
    `${import.meta.env.VITE_API_URL}/api/auth/login`,
    credentials,
    { headers: { "Content-Type": "application/json" } }
  );
  console.log(response.data);
  return response.data;
}

export function useLogin() {
  return useMutation<LoginResponse, Error, LoginCredentials>({
    mutationFn: loginUser,
  });
}
