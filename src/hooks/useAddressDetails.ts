import { useMutation } from '@tanstack/react-query'
import axios from 'axios'
import { useApplicationStore } from '@/store/applicationStore'

interface AddressDetailsData {
    present_address: string
    present_pincode: number
    present_state: string
    present_district: string
    permanent_address: string
    permanent_pincode: number
    parmanent_state: string
    parmanent_district: string
    status: string
}

interface AddressDetailsResponse {
    success: boolean
    message: string
}

const createAddressDetails = async (data: AddressDetailsData, applicationId: string): Promise<AddressDetailsResponse> => {
    if (!applicationId) {
        throw new Error('No application ID found')
    }

    const response = await axios.post(
        `${import.meta.env.VITE_API_URL}/api/application/create-address-details/${applicationId}`,
        data,
        {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        }
    )
    return response.data
}

export const useAddressDetails = () => {
    const applicationId = useApplicationStore((state) => state.applicationId)

    return useMutation({
        mutationFn: (data: AddressDetailsData) => createAddressDetails(data, applicationId!),
        onSuccess: (data) => {
            console.log('Address details submitted successfully:', data)
        },
        onError: (error) => {
            console.error('Error submitting address details:', error)
        }
    })
} 