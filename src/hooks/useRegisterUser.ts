import { useMutation } from "@tanstack/react-query";
import axios from "axios";

type PostApiOptions = {
  url: string;
  body: any;
  headers?: Record<string, string>;
};

async function registerUser<T = any>({ url, body, headers = {} }: PostApiOptions): Promise<T> {
  const response = await axios.post(
    url,
    body,
    { headers: { "Content-Type": "application/json", ...headers } }
  );
  return response.data;
}

export function useRegisterUser<T = any>() {
  return useMutation<T, Error, PostApiOptions>({
    mutationFn: registerUser,
  });
}

