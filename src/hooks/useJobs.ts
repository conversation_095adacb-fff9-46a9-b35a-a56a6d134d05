import { useQuery } from '@tanstack/react-query'
import axios from 'axios'

export interface Job {
    id: number
    project: string
    position: string
    post_code: string
    min_age: number
    max_age: number
    level: string
    group: string
    vacancies: number
    reservation_category: string
    qualification: string
    experience: string
    last_date: string
    createdAt: string
    updatedAt: string
}

const fetchJobs = async (): Promise<Job[]> => {
    const response = await axios.get(`${import.meta.env.VITE_API_URL}/api/application/get-all-jobs`)
    return response.data.jobs
}

export const useJobs = () => {
    return useQuery({
        queryKey: ['jobs'],
        queryFn: fetchJobs
    })
}