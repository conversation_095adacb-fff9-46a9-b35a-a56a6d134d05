import { useQuery } from '@tanstack/react-query'
import axios from 'axios'

export interface JobApplication {
    id: number;
    project: string;
    position: string;
    post_code: string;
    min_age: string;
    max_age: string;
    level: string;
    group: string;
    vacancies: number;
    reservation_category: string;
    qualification: string;
    experience: string;
    last_date: string;
    createdAt: string;
    updatedAt: string;
  }
  
  export interface Application {
    id: number;
    user_id: number;
    application_id: string;
    status: string;
    appliedAt: string; 
    job_id: number;
    createdAt: string;
    updatedAt: string;
    job_application: JobApplication;
    applications: Application[];
  }
  
    const getApplicationHistory = async (): Promise<Application[]> => {
        const response = await axios.get(
            `${import.meta.env.VITE_API_URL}/api/application/get-user-application-history`,
        {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        }
    )
    return response.data
}

export const useGetApplicationHistory = () => {
    return useQuery({
        queryKey: ['applicationHistory'],
        queryFn: getApplicationHistory,
    })
} 