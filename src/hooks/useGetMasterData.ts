import { useQuery } from '@tanstack/react-query'
import axios from 'axios'

export interface  District {
    id: number
    name : string 
    districs:string
}

 const fetchDistrictDetails = async (): Promise<District[]> => {
    const response = await axios.get(`${import.meta.env.VITE_API_URL}/api/application/get-all-districts`)
    console.log("from hook ", response.data)
    return response.data
}

export const useGetDistricts = () => {
    return useQuery({
      queryKey: ['districtDetails'],
      queryFn: fetchDistrictDetails,
    })
  }
  