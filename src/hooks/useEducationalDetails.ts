import { useMutation } from '@tanstack/react-query'
import axios from 'axios'
import { useApplicationStore } from '@/store/applicationStore'

interface EducationalDetailsData {
    qualification: string
    school_college: string
    subject_stream_course: string
    year_of_passing: string
    board_council_university: string
    percentage_marks_cgpa: string
    status: string
}

interface EducationalDetailsResponse {
    success: boolean
    message: string
}

const createEducationalDetails = async (data: EducationalDetailsData, applicationId: string): Promise<EducationalDetailsResponse> => {
    if (!applicationId) {
        throw new Error('No application ID found')
    }

    const requestData = {
        educations: [{
            
            qualification: data.qualification,
            school_college: data.school_college,
            subject_stream_course: data.subject_stream_course,
            year_of_passing: data.year_of_passing,
            board_council_university: data.board_council_university,
            percentage_marks_cgpa: data.percentage_marks_cgpa,
            status: data.status
        }]
    }

    const response = await axios.post(
        `${import.meta.env.VITE_API_URL}/api/application/create-educational-details/${applicationId}`,
        requestData,
        {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        }
    )
    return response.data
}

export const useEducationalDetails = () => {
    const applicationId = useApplicationStore((state) => state.applicationId)

    return useMutation({
        mutationFn: (data: EducationalDetailsData) => createEducationalDetails(data, applicationId!),
        onSuccess: (data) => {
            console.log('Educational details submitted successfully:', data)
        },
        onError: (error) => {
            console.error('Error submitting educational details:', error)
        }
    })
} 