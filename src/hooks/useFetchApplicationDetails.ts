import { useQuery } from '@tanstack/react-query'
import axios from 'axios'

export interface ApplicationDetails {
    id: number
    project: string
    position: string
    post_code: string
    min_age: number
    max_age: number
    level: string
    group: string
    vacancies: number
    reservation_category: string
    qualification: string
    experience: string
    last_date: string
    createdAt: string
    updatedAt: string 
    application: any 
    
}

 const fetchApplicationDetails = async (applicationId: string): Promise<ApplicationDetails> => {
    
    const response = await axios.get(`${import.meta.env.VITE_API_URL}/api/application/applicationdetails-by-id/${applicationId}`)
    return response.data
}
export const useApplicationDetails = (applicationId: string) => {
    return useQuery({
      queryKey: ['applicationDetails'],
      queryFn: () => fetchApplicationDetails(applicationId),
      enabled: !!applicationId,
    })
  }
  