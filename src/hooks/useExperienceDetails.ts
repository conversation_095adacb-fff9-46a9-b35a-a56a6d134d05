import { useMutation } from '@tanstack/react-query'
import axios from 'axios'
import { useApplicationStore } from '@/store/applicationStore'

interface ExperienceDetailsData {
    experience_level: string
    organisation: string
    designation: string
    from_date: string
    to_date: string
    status: string
}

interface ExperienceDetailsResponse {
    success: boolean
    message: string
}

const createExperienceDetails = async (data: ExperienceDetailsData, applicationId: string): Promise<ExperienceDetailsResponse> => {
    if (!applicationId) {
        throw new Error('No application ID found')
    }

    const requestData = {
        experience: [{
          
            experience_level: data.experience_level,
            organisation: data.organisation,
            designation: data.designation,
            from_date: data.from_date,
            to_date: data.to_date,
            status: data.status
        }]
    }

    const response = await axios.post(
        `${import.meta.env.VITE_API_URL}/api/application/create-experience-details/${applicationId}`,
        requestData,
        {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        }
    )
    return response.data
}

export const useExperienceDetails = () => {
    const applicationId = useApplicationStore((state) => state.applicationId)

    return useMutation({
        mutationFn: (data: ExperienceDetailsData) => createExperienceDetails(data, applicationId!),
        onSuccess: (data) => {
            console.log('Experience details submitted successfully:', data)
        },
        onError: (error) => {
            console.error('Error submitting experience details:', error)
        }
    })
} 