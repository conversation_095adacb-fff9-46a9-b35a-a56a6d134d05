import { useMutation } from '@tanstack/react-query'
import axios from 'axios'
import { useApplicationStore } from '@/store/applicationStore'

interface PersonalDetailsData {
    fathers_name: string
    mothers_name: string
    marital_status: string
    status: string
}

interface PersonalDetailsResponse {
    success: boolean
    message: string
}

const createPersonalDetails = async (data: PersonalDetailsData, applicationId: string): Promise<PersonalDetailsResponse> => {
    if (!applicationId) {
        throw new Error('No application ID found')
    }

    const response = await axios.post(
        `${import.meta.env.VITE_API_URL}/api/application/create-personal-details/${applicationId}`,
        data,
        {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        }
    )
    return response.data
}

export const usePersonalDetails = () => {
    const applicationId = useApplicationStore((state) => state.applicationId)

    return useMutation({
        mutationFn: (data: PersonalDetailsData) => createPersonalDetails(data, applicationId!),
        onSuccess: (data) => {
            console.log('Personal details submitted successfully:', data)
        },
        onError: (error) => {
            console.error('Error submitting personal details:', error)
        }
    })
} 