import { useState } from 'react'
import { PersonalInfoForm } from './PersonalInfoForm'
import { EducationalInfoForm } from './EducationalInfoForm'
import { WorkExperienceForm } from './WorkExperienceForm'


export function ApplicationForm() {
    const [step, setStep] = useState(1)
    const [formData, setFormData] = useState({
        personalInfo: {},
        educationalInfo: {},
        workExperience: {}
    })

    const handlePersonalInfoSubmit = (data: any) => {
        setFormData(prev => ({ ...prev, personalInfo: data }))
        setStep(2)
    }

    const handleEducationalInfoSubmit = (data: any) => {
        setFormData(prev => ({ ...prev, educationalInfo: data }))
        setStep(3)
    }

    const handleWorkExperienceSubmit = (data: any) => {
        setFormData(prev => ({ ...prev, workExperience: data }))
       
        console.log('Final form data:', { ...formData, workExperience: data })
    }

    const handleBack = () => {
        setStep(prev => prev - 1)
    }

    return (
        <div className="max-w-7xl mx-auto p-6">
            <div className="mb-8">
                <div className="flex justify-between items-center mb-4">
                    <h1 className="text-3xl font-bold">Application Form</h1>
                
                    <div className="text-sm text-gray-500">Step {step} of 3</div>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(step / 3) * 100}%` }}
                    />
                </div>
            </div>

            {step === 1 && (
                <PersonalInfoForm 
                    onSubmit={handlePersonalInfoSubmit}
                />
            )}

            {step === 2 && (
                <EducationalInfoForm 
                    onSubmit={handleEducationalInfoSubmit}
                    onBack={handleBack}
                />
            )}

            {step === 3 && (
                <WorkExperienceForm 
                    onSubmit={handleWorkExperienceSubmit}
                    onBack={handleBack}
                />
            )}
        </div>
    )
} 