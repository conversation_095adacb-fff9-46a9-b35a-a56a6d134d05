import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import {
  CalendarDays,
  FileText,
  Mail,
  Phone,
  User,
  GraduationCap,
  BookOpen,
  Briefcase,
  Download,
  Send,
  Eye,
} from "lucide-react"
import { useSubmitApplication } from "@/hooks/useSubmitApplication"
import jsPDF from "jspdf";

interface ApplicationData {
  success: boolean
  message: string
  application: {
    id: number
    user_id: number
    application_id: string
    status: string
    appliedAt: string
    job_id: number
    createdAt: string
    updatedAt: string
    user_details: {
      id: number
      email: string
      first_name: string
      middle_name: string
      last_name: string
      role: string
      dob: string
      phoneNumber: string
      Qualification: string
      pwbd_status: string
      gender: string
      user_application_id: string
      createdAt: string
      updatedAt: string
    }
    user_personal_details: {
      id: number
      application_id: string
      fathers_name: string
      mothers_name: string
      marital_status: string
      status: string
      createdAt: string
      updatedAt: string
    } | null
    user_address: any
    educations: Array<{
      id: number
      application_id: string
      qualification: string | null
      school_college: string | null
      subject_stream_course: string | null
      year_of_passing: string | null
      board_council_university: string | null
      percentage_marks_cgpa: string | null
      status: string
      createdAt: string
      updatedAt: string
    }>
    user_experience: Array<{
      id: number
      application_id: string
      organisation: string | null
      designation: string | null
      from_date: string | null
      to_date: string | null
      experience: number | null
      status: string
      createdAt: string
      updatedAt: string
    }>
    user_documents: Array<{
      id: number
      application_id: string
      file_url: string
      name: string
      status: string
      createdAt: string
      updatedAt: string
    }>
  }
}

interface ApplicationAcknowledgementProps {
  data: ApplicationData
}

export default function ApplicationAcknowledgement({ data }: ApplicationAcknowledgementProps) {
  const [selectedDocument, setSelectedDocument] = useState<{ name: string; url: string; type: string } | null>(null)
  const submitApplication = useSubmitApplication();
  const { application } = data
  const validEducations = application.educations.filter(
    (edu) => edu.qualification && edu.school_college && edu.year_of_passing,
  )
  const validExperiences = application.user_experience.filter(
    (exp) => exp.organisation && exp.designation && exp.from_date,
  )


  const calculateTotalExperience = () => {
    const totalMonths = validExperiences.reduce((total, exp) => {
      return total + (exp.experience || 0)
    }, 0)

    const years = Math.floor(totalMonths / 12)
    const months = totalMonths % 12

    if (years > 0 && months > 0) {
      return `${years} year${years > 1 ? "s" : ""} ${months} month${months > 1 ? "s" : ""}`
    } else if (years > 0) {
      return `${years} year${years > 1 ? "s" : ""}`
    } else if (months > 0) {
      return `${months} month${months > 1 ? "s" : ""}`
    }
    return "No experience"
  }

  const handleViewDocument = (doc: (typeof application.user_documents)[0]) => {
    setSelectedDocument({
      name: formatDocumentName(doc.name),
      url: `https://${doc.file_url}`,
      type: "image", // Assuming webp images based on the URLs
    })
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "submitted":
        return "bg-blue-100 text-blue-800 border-blue-200"
      case "approved":
        return "bg-green-100 text-green-800 border-green-200"
      case "rejected":
        return "bg-red-100 text-red-800 border-red-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const formatDocumentName = (doc: string) => {
    return doc.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("en-IN", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    })
  }

  const formatExperienceDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-IN", {
      month: "short",
      year: "numeric",
    })
  }

  const getFullName = () => {
    const { first_name, middle_name, last_name } = application.user_details
    return [first_name, middle_name, last_name].filter(Boolean).join(" ")
  }

  const handleDownload = () => {
    const doc = new jsPDF();
    let y = 10;
    doc.setFontSize(16);
    doc.text("Application Acknowledgement", 10, y);
    y += 10;
    doc.setFontSize(12);
    doc.text(`Application ID: ${application.application_id}`, 10, y);
    y += 8;
    doc.text(`Status: ${application.status}`, 10, y);
    y += 8;
    doc.text(`Applied At: ${formatDate(application.appliedAt)}`, 10, y);
    y += 8;
    doc.text(`Name: ${getFullName()}`, 10, y);
    y += 8;
    doc.text(`Email: ${application.user_details.email}`, 10, y);
    y += 8;
    doc.text(`Phone: ${application.user_details.phoneNumber}`, 10, y);
    y += 8;
    doc.text(`Gender: ${application.user_details.gender}`, 10, y);
    y += 8;
    doc.text(`Date of Birth: ${new Date(application.user_details.dob).toLocaleDateString("en-IN")}`, 10, y);
    y += 8;
    if (application.user_personal_details) {
      doc.text(`Father's Name: ${application.user_personal_details.fathers_name}`, 10, y);
      y += 8;
      doc.text(`Mother's Name: ${application.user_personal_details.mothers_name}`, 10, y);
      y += 8;
      doc.text(`Marital Status: ${application.user_personal_details.marital_status}`, 10, y);
      y += 8;
    }
    // Education Section
    if (validEducations.length > 0) {
      y += 4;
      doc.setFontSize(14);
      doc.text("Educational Details", 10, y);
      y += 8;
      doc.setFontSize(12);
      doc.text(`Highest Qualification: ${application.user_details.Qualification}`, 10, y);
      y += 8;
      validEducations.forEach((edu) => {
        doc.text(`- ${edu.qualification} (${edu.year_of_passing})`, 12, y);
        y += 7;
        doc.text(`  School/College: ${edu.school_college}`, 14, y);
        y += 7;
        doc.text(`  Board/University: ${edu.board_council_university}`, 14, y);
        y += 7;
        doc.text(`  Subject/Stream: ${edu.subject_stream_course}`, 14, y);
        y += 7;
        doc.text(`  Percentage/CGPA: ${edu.percentage_marks_cgpa}`, 14, y);
        y += 8;
      });
    }
    // Experience Section
    if (validExperiences.length > 0) {
      y += 4;
      doc.setFontSize(14);
      doc.text("Experience Details", 10, y);
      y += 8;
      doc.setFontSize(12);
      doc.text(`Total Experience: ${calculateTotalExperience()}`, 10, y);
      y += 8;
      validExperiences.forEach((exp) => {
        doc.text(`- ${exp.designation} at ${exp.organisation}`, 12, y);
        y += 7;
        doc.text(`  From: ${formatExperienceDate(exp.from_date!)} To: ${exp.to_date ? formatExperienceDate(exp.to_date) : "Present"}`, 14, y);
        y += 7;
        doc.text(`  Experience: ${exp.experience} month${exp.experience !== 1 ? "s" : ""}`, 14, y);
        y += 8;
      });
    }
    // Documents Section
    if (application.user_documents.length > 0) {
      y += 4;
      doc.setFontSize(14);
      doc.text("Uploaded Documents", 10, y);
      y += 8;
      doc.setFontSize(12);
      application.user_documents.forEach((docu) => {
        doc.text(`- ${formatDocumentName(docu.name)}`, 12, y);
        y += 7;
      });
      y += 4;
      doc.text(`Total documents uploaded: ${application.user_documents.length}`, 10, y);
      y += 8;
    }
    // Footer
    y += 6;
    doc.setFontSize(10);
    doc.text("Please save this acknowledgement for your records. You will be notified via email about the status updates.", 10, y);
    doc.save(`Acknowledgement_${application.application_id}.pdf`);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Application Acknowledgement</h1>
          <p className="text-red-600">Please review once before submit </p>
        </div>

        <Card className="mb-6">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl text-gray-800">Application Summary</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-500">Application ID</p>
                <p className="font-mono text-sm bg-gray-100 px-3 py-2 rounded border">{application.application_id}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-500">Status</p>
                <Badge className={`${getStatusColor(application.status)} capitalize`}>{application.status}</Badge>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-500">Applied At</p>
                <div className="flex items-center gap-2 text-sm text-gray-700">
                  <CalendarDays className="h-4 w-4" />
                  {formatDate(application.appliedAt)}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="mb-6">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl text-gray-800 flex items-center gap-2">
              <User className="h-5 w-5" />
              Personal Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <User className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-500">Name</p>
                    <p className="text-gray-900">{getFullName()}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-500">Email</p>
                    <p className="text-gray-900">{application.user_details.email}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-500">Phone</p>
                    <p className="text-gray-900">{application.user_details.phoneNumber}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <User className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-500">Gender</p>
                    <p className="text-gray-900 capitalize">{application.user_details.gender}</p>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <CalendarDays className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-500">Date of Birth</p>
                    <p className="text-gray-900">
                      {new Date(application.user_details.dob).toLocaleDateString("en-IN")}
                    </p>
                  </div>
                </div>
                {application.user_personal_details && (
                  <>
                    <div className="flex items-center gap-3">
                      <User className="h-4 w-4 text-gray-500" />
                      <div>
                        <p className="text-sm font-medium text-gray-500">Father's Name</p>
                        <p className="text-gray-900">{application.user_personal_details.fathers_name}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <User className="h-4 w-4 text-gray-500" />
                      <div>
                        <p className="text-sm font-medium text-gray-500">Mother's Name</p>
                        <p className="text-gray-900">{application.user_personal_details.mothers_name}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <User className="h-4 w-4 text-gray-500" />
                      <div>
                        <p className="text-sm font-medium text-gray-500">Marital Status</p>
                        <p className="text-gray-900 capitalize">{application.user_personal_details.marital_status}</p>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {validEducations.length > 0 && (
          <Card className="mb-6">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl text-gray-800 flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Educational Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="flex items-center gap-3 mb-4">
                  <GraduationCap className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-500">Highest Qualification</p>
                    <p className="text-gray-900 font-medium">{application.user_details.Qualification}</p>
                  </div>
                </div>

                <Separator />

                <div className="space-y-6">
                  {validEducations.map((education) => (
                    <div key={education.id} className="border-l-2 border-blue-200 pl-4 space-y-2">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                        <h4 className="font-medium text-gray-800 capitalize">{education.qualification}</h4>
                        <span className="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded">
                          {education.year_of_passing}
                        </span>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">School/College</p>
                          <p className="text-gray-900">{education.school_college}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Board/University</p>
                          <p className="text-gray-900">{education.board_council_university}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Subject/Stream</p>
                          <p className="text-gray-900">{education.subject_stream_course}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Percentage/CGPA</p>
                          <p className="text-gray-900 font-medium">{education.percentage_marks_cgpa}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {validExperiences.length > 0 && (
          <Card className="mb-6">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl text-gray-800 flex items-center gap-2">
                <Briefcase className="h-5 w-5" />
                Experience Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="flex items-center gap-3 mb-4">
                  <Briefcase className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-500">Total Experience</p>
                    <p className="text-gray-900 font-medium">{calculateTotalExperience()}</p>
                  </div>
                </div>

                <Separator />

                <div className="space-y-6">
                  {validExperiences.map((experience) => (
                    <div key={experience.id} className="border-l-2 border-blue-200 pl-4 space-y-2">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                        <h4 className="font-medium text-gray-800">{experience.designation}</h4>
                        <span className="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded">
                          {formatExperienceDate(experience.from_date!)} -{" "}
                          {experience.to_date ? formatExperienceDate(experience.to_date) : "Present"}
                        </span>
                      </div>
                      <p className="text-gray-700 font-medium">{experience.organisation}</p>
                      <p className="text-sm text-gray-600">
                        Experience: {experience.experience} month{experience.experience !== 1 ? "s" : ""}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader className="pb-4">
            <CardTitle className="text-xl text-gray-800 flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Uploaded Documents
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              {application.user_documents.map((doc) => (
                <div
                  key={doc.id}
                  className="flex items-center justify-between gap-2 p-3 bg-gray-50 rounded-lg border hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    <FileText className="h-4 w-4 text-gray-500 flex-shrink-0" />
                    <span className="text-sm text-gray-700 truncate">{formatDocumentName(doc.name)}</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleViewDocument(doc)}
                    className="h-8 w-8 p-0 hover:bg-blue-100"
                  >
                    <Eye className="h-4 w-4 text-blue-600" />
                  </Button>
                </div>
              ))}
            </div>
            <Separator className="my-4" />
            <p className="text-sm text-gray-600">
              Total documents uploaded: <span className="font-medium">{application.user_documents.length}</span>
            </p>

            {/* Document Viewer Dialog */}
            <Dialog open={!!selectedDocument} onOpenChange={() => setSelectedDocument(null)}>
              <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    {selectedDocument?.name}
                  </DialogTitle>
                </DialogHeader>
                <div className="flex-1 overflow-auto">
                  {selectedDocument && (
                    <div className="flex justify-center items-center min-h-[400px] bg-gray-50 rounded-lg">
                      <img
                        src={selectedDocument.url || "/placeholder.svg"}
                        alt={selectedDocument.name}
                        className="max-w-full max-h-[70vh] object-contain rounded"
                        crossOrigin="anonymous"
                      />
                    </div>
                  )}
                </div>
              </DialogContent>
            </Dialog>
          </CardContent>
        </Card>

        <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            className="flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            onClick={() => submitApplication.mutate(application.application_id)}
            disabled={submitApplication.isPending}
          >
            <Send className="h-4 w-4" />
            {submitApplication.isPending ? "Submitting..." : "Submit Application"}
          </Button>
          <Button
            className="flex items-center justify-center gap-2 bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            onClick={handleDownload}
          >
            <Download className="h-4 w-4" />
            Download Acknowledgement
          </Button>
        </div>

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            Please save this acknowledgement for your records. You will be notified via email about the status updates.
          </p>
        </div>
      </div>
    </div>
  )
}
