import React, { useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { useUploadDocuments } from '@/hooks/useUploadDocuments';
import { toast } from 'sonner';
import { Upload, FileText, CheckCircle, AlertCircle } from 'lucide-react';

interface DocumentUploadFormProps {
  applicationId: string;
  onBack?: () => void;
  onSuccess?: () => void;
}

interface FileState {
  [key: string]: File | null;
  passport: File | null;
  sign: File | null;
  marksheet_x: File | null;
  marksheet_xii: File | null;
  graduation: File | null;
}

interface DocumentType {
  key: keyof FileState;
  label: string;
  description: string;
  accept: string;
  required: boolean;
}

const documentTypes: DocumentType[] = [
  {
    key: 'passport',
    label: 'Passport Photo',
    description: 'Recent passport-size photograph (JPEG, PNG, or PDF)',
    accept: 'image/jpeg,image/png,image/jpg,application/pdf',
    required: true,
  },
  {
    key: 'sign',
    label: 'Signature',
    description: 'Clear signature on white background (JPEG, PNG, or PDF)',
    accept: 'image/jpeg,image/png,image/jpg,application/pdf',
    required: true,
  },
  {
    key: 'marksheet_x',
    label: 'Class 10 Marksheet',
    description: 'Official Class 10 certificate or marksheet',
    accept: 'image/*,application/pdf',
    required: true,
  },
  {
    key: 'marksheet_xii',
    label: 'Class 12 Marksheet',
    description: 'Official Class 12 certificate or marksheet',
    accept: 'image/*,application/pdf',
    required: true,
  },
  {
    key: 'graduation',
    label: 'Graduation Certificate',
    description: 'Final degree certificate or consolidated marksheet',
    accept: 'image/*,application/pdf',
    required: true,
  },
];

export function DocumentUploadForm({ applicationId, onBack, onSuccess }: DocumentUploadFormProps) {
  const [files, setFiles] = useState<FileState>({
    passport: null,
    sign: null,
    marksheet_x: null,
    marksheet_xii: null,
    graduation: null,
  });

  const [dragOver, setDragOver] = useState<string | null>(null);
  const uploadMutation = useUploadDocuments();

  const inputRefs = {
    passport: useRef<HTMLInputElement>(null),
    sign: useRef<HTMLInputElement>(null),
    marksheet_x: useRef<HTMLInputElement>(null),
    marksheet_xii: useRef<HTMLInputElement>(null),
    graduation: useRef<HTMLInputElement>(null),
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, type: keyof FileState) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('File size must be less than 5MB');
        return;
      }
      
      setFiles(prev => ({ ...prev, [type]: file }));
      toast.success(`${file.name} uploaded successfully`);
    }
  };

  const handleBoxClick = (type: keyof FileState) => {
    inputRefs[type].current?.click();
  };

  const handleDragOver = (e: React.DragEvent, type: keyof FileState) => {
    e.preventDefault();
    setDragOver(type as string);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(null);
  };

  const handleDrop = (e: React.DragEvent, type: keyof FileState) => {
    e.preventDefault();
    setDragOver(null);
    
    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles && droppedFiles[0]) {
      const file = droppedFiles[0];
      
      // Validate file size
      if (file.size > 5 * 1024 * 1024) {
        toast.error('File size must be less than 5MB');
        return;
      }
      
      setFiles(prev => ({ ...prev, [type]: file }));
      toast.success(`${file.name} uploaded successfully`);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!applicationId) {
      toast.error('Application ID is missing. Cannot upload documents.');
      return;
    }

    // Validate required files
    const missingFiles = documentTypes
      .filter(doc => doc.required && !files[doc.key])
      .map(doc => doc.label);

    if (missingFiles.length > 0) {
      toast.error(`Please upload the following required documents: ${missingFiles.join(', ')}`);
      return;
    }

    uploadMutation.mutate(
    
      { applicationId, files },
      {
        onSuccess: () => {
          toast.success('All documents uploaded successfully!');
          if (onSuccess) onSuccess();
        },
        onError: (error: any) => {
          toast.error(error?.message || 'Failed to upload documents. Please try again.');
        },
      }
    );
  };

  const getFileSize = (size: number) => {
    if (size < 1024) return `${size} B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
    return `${(size / (1024 * 1024)).toFixed(1)} MB`;
  };

  const renderUploadBox = (docType: DocumentType) => {
    const file = files[docType.key];
    const isDraggedOver = dragOver === docType.key;
    const isUploaded = !!file;

    return (
      <div
        className={`
          relative border-2 border-dashed rounded-xl p-6 text-center cursor-pointer 
          transition-all duration-200 hover:shadow-lg group
          ${isDraggedOver 
            ? 'border-blue-500 bg-blue-50 shadow-lg' 
            : isUploaded 
              ? 'border-green-500 bg-green-50' 
              : 'border-gray-300 bg-white hover:border-gray-400 hover:bg-gray-50'
          }
        `}
        onClick={() => handleBoxClick(docType.key)}
        onDragOver={(e) => handleDragOver(e, docType.key)}
        onDragLeave={handleDragLeave}
        onDrop={(e) => handleDrop(e, docType.key)}
      >
        <input
          type="file"
          ref={inputRefs[docType.key]}
          style={{ display: 'none' }}
          onChange={(e) => handleFileChange(e, docType.key)}
          accept={docType.accept}
        />
        
        {/* Required badge */}
        {docType.required && (
          <div className="absolute top-2 right-2">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
              Required
            </span>
          </div>
        )}

        <div className="flex flex-col items-center space-y-3">
          {isUploaded ? (
            <CheckCircle className="w-12 h-12 text-green-500" />
          ) : (
            <Upload className={`w-12 h-12 transition-colors ${isDraggedOver ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-600'}`} />
          )}
          
          <div>
            <h3 className="font-semibold text-gray-900 mb-1">{docType.label}</h3>
            <p className="text-sm text-gray-600 mb-2">{docType.description}</p>
            
            {file ? (
              <div className="space-y-1">
                <div className="flex items-center justify-center space-x-2 text-green-700">
                  <FileText className="w-4 h-4" />
                  <span className="font-medium text-sm">{file.name}</span>
                </div>
                <div className="text-xs text-gray-500">
                  {getFileSize(file.size)}
                </div>
              </div>
            ) : (
              <div className="text-gray-500">
                <p className="text-sm font-medium">Click to upload or drag and drop</p>
                <p className="text-xs mt-1">Max file size: 5MB</p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const uploadedCount = Object.values(files).filter(Boolean).length;
  const totalCount = documentTypes.length;
  const progressPercentage = (uploadedCount / totalCount) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8 px-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Document Upload</h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Please upload all required documents to proceed with your application. 
            All files should be clear, readable, and in the specified formats.
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-8 bg-white rounded-lg p-4 shadow-sm">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Upload Progress</span>
            <span className="text-sm text-gray-600">{uploadedCount} of {totalCount} documents</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Upload Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {documentTypes.map((docType) => (
              <div key={docType.key}>
                {renderUploadBox(docType)}
              </div>
            ))}
          </div>

          {/* Important Notes */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-8">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-blue-900 mb-2">Important Guidelines</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Ensure all documents are clear and readable</li>
                  <li>• Maximum file size allowed is 5MB per document</li>
                  <li>• Accepted formats: JPEG, PNG, PDF</li>
                  <li>• All marked documents are required for submission</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-6 border-t border-gray-200">
            {onBack && (
              <Button 
                type="button" 
                variant="outline" 
                onClick={onBack}
                className="px-6 py-2"
              >
                Back
              </Button>
            )}
            <div className="flex-1" />
            <Button 
              type="submit" 
              disabled={uploadMutation.status === 'pending' || uploadedCount === 0}
              className="px-8 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium"
            >
              {uploadMutation.status === 'pending' ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>Uploading...</span>
                </div>
              ) : (
                'Continue'
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}