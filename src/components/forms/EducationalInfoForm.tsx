import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useEducationalDetails } from '@/hooks/useEducationalDetails'
import { toast } from 'sonner'
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from "@/components/ui/dropdown-menu"

const educationalInfoSchema = z.object({
  education: z.array(
    z.object({
      qualification: z.string().min(1, 'Qualification is required'),
      school_college: z.string().min(1, 'School/College is required'),
      subject_stream_course: z.string().min(1, 'Subject/Stream/Course is required'),
      year_of_passing: z.string().min(1, 'Year of passing is required'),
      board_council_university: z.string().min(1, 'Board/Council/University is required'),
      percentage_marks_cgpa: z.string().min(1, 'Percentage/Marks/CGPA is required'),
      status: z.string().default('saved'),
    })
  )
})

interface EducationalInfoFormProps {
  onSubmit: (data: any) => void
  onBack?: () => void
  onNext?: () => void
}
//onSubmit
export function EducationalInfoForm({  onBack, onNext }: EducationalInfoFormProps) {
  const form = useForm({
    resolver: zodResolver(educationalInfoSchema),
    defaultValues: {
      education: [
        {
          qualification: '',
          school_college: '',
          subject_stream_course: '',
          year_of_passing: '',
          board_council_university: '',
          percentage_marks_cgpa: '',
          status: 'saved',
        },
      ],
    },
  })

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'education',
  })

  const createEducationalDetails = useEducationalDetails()

  const handleNext = async (data: any) => {
    console.log(form.formState.errors)
    try {
      for (const edu of data.education) {
        const result = await createEducationalDetails.mutateAsync(edu)
        if (result.success) {
          toast.success('Education details saved successfully')
        } else {
          toast.error('Failed to save education details')
        }
      }
      onNext?.()
    } catch (error) {
      toast.error('Failed to save education details')
    }
  }

  return (
    <form onSubmit={form.handleSubmit(handleNext)} className="space-y-6">
      <h2 className="text-2xl font-bold mb-6">Educational Information</h2>
      <div className="space-y-8">
        {fields.map((field, idx) => (
          <fieldset key={field.id} className="border rounded-lg p-4 relative">
            <legend className="font-semibold text-lg mb-2">Qualification {idx + 1}</legend>
            {fields.length > 1 && (
              <button
                type="button"
                onClick={() => remove(idx)}
                className="absolute top-2 right-2 text-red-500 hover:text-red-700 text-sm"
              >
                Remove
              </button>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <Label htmlFor={`education.${idx}.qualification`}>Qualification</Label>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Input
                      type="text"
                      id={`education.${idx}.qualification`}
                      value={form.watch(`education.${idx}.qualification`) || ""}
                      placeholder="Select qualification"
                      readOnly
                      className="cursor-pointer"
                    />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    {['class 10','class 12','Graduation','Masters','Phd'].map((q) => (
                      <DropdownMenuItem
                        key={q}
                        onClick={() => form.setValue(`education.${idx}.qualification`, q)}
                      >
                        {q}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
                {form.formState.errors.education?.[idx]?.qualification && (
                  <p className="text-red-500 text-sm">{form.formState.errors.education[idx]?.qualification?.message as string}</p>
                )}
              </div>
              <div>
                <Label htmlFor={`education.${idx}.school_college`}>School/College</Label>
                <Input {...form.register(`education.${idx}.school_college`)} />
                {form.formState.errors.education?.[idx]?.school_college && (
                  <p className="text-red-500 text-sm">{form.formState.errors.education[idx]?.school_college?.message as string}</p>
                )}
              </div>
              <div>
                <Label htmlFor={`education.${idx}.subject_stream_course`}>Subject/Stream/Course</Label>
                <Input {...form.register(`education.${idx}.subject_stream_course`)} />
                {form.formState.errors.education?.[idx]?.subject_stream_course && (
                  <p className="text-red-500 text-sm">{form.formState.errors.education[idx]?.subject_stream_course?.message as string}</p>
                )}
              </div>
              <div>
                <Label htmlFor={`education.${idx}.year_of_passing`}>Year of Passing</Label>
                <Input type="number" {...form.register(`education.${idx}.year_of_passing`)} />
                {form.formState.errors.education?.[idx]?.year_of_passing && (
                  <p className="text-red-500 text-sm">{form.formState.errors.education[idx]?.year_of_passing?.message as string}</p>
                )}
              </div>
              <div>
                <Label htmlFor={`education.${idx}.board_council_university`}>Board/Council/University</Label>
                <Input {...form.register(`education.${idx}.board_council_university`)} />
                {form.formState.errors.education?.[idx]?.board_council_university && (
                  <p className="text-red-500 text-sm">{form.formState.errors.education[idx]?.board_council_university?.message as string}</p>
                )}
              </div>
              <div>
                <Label htmlFor={`education.${idx}.percentage_marks_cgpa`}>Percentage/Marks/CGPA</Label>
                <Input {...form.register(`education.${idx}.percentage_marks_cgpa`)} />
                {form.formState.errors.education?.[idx]?.percentage_marks_cgpa && (
                  <p className="text-red-500 text-sm">{form.formState.errors.education[idx]?.percentage_marks_cgpa?.message as string}</p>
                )}
              </div>
            </div>
          </fieldset>
        ))}
        <Button
          type="button"
          variant="outline"
          onClick={() =>
            append({
              qualification: '',
              school_college: '',
              subject_stream_course: '',
              year_of_passing: '',
              board_council_university: '',
              percentage_marks_cgpa: '',
              status: 'saved',
            })
          }
        >
          Add Qualification
        </Button>
      </div>
      <div className="flex gap-4">
        {onBack && <Button type="button" variant="outline" onClick={onBack}>Back</Button>}
        <Button type="submit" className="flex-1">Next</Button>
      </div>
    </form>
  )
} 