import { useForm } from 'react-hook-form'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useAddressDetails } from '@/hooks/useAddressDetails'
import { toast } from 'sonner'
import { useGetDistricts } from '@/hooks/useGetMasterData'


interface AdditionalInfoFormProps {
    onSubmit: (data: any) => void
    onBack?: () => void
    applicationId?: string
    /**
     * Called after successful address save, to move to the next step (upload docs)
     */
    onNext?: () => void
}

export function AdditionalInfoForm({ onBack, applicationId, onNext }: AdditionalInfoFormProps) {
 
    const createAddressDetails = useAddressDetails()
    const form = useForm({
        defaultValues: {
            present_address: '',
            present_pincode: 0,
            present_state: '',
            present_district: '',
            permanent_address: '',
            permanent_pincode: 0,
            parmanent_state: '',
            parmanent_district: '',
           
        }
    })


    const { data: district} = useGetDistricts()
    // console.log("Additional ",district?.districts);

 
    const handleNext = async (data: any) => {
        console.log('Next button clicked!')
        console.log('Form data being submitted:', data)
        
        try {
            const addressData = {
                ...data,
                id: applicationId ? parseInt(applicationId) : 0,
                status: 'saved'
            }
            
            console.log('Address data being sent to API:', addressData)
            
            const result = await createAddressDetails.mutateAsync(addressData)
            
            console.log('Address API response:', result)
            
            if (result.success) {
                toast.success('Address details saved successfully')
                if (onNext) onNext();
            } else {
                toast.error('Failed to save address details')
            }
        } catch (error) {
            console.error('Error saving address details:', error)
            toast.error('Failed to save address details')
        }
    }

  

   

    return (
        <form onSubmit={form.handleSubmit(handleNext)} className="space-y-6">
            <h2 className="text-2xl font-bold mb-6">Additional Information</h2>
            <div className="space-y-6">
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Present Address</h3>
                    <div className="space-y-2">
                        <Label htmlFor="present_address">Address</Label>
                        <textarea
                            {...form.register('present_address')}
                            className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
                            rows={4}
                        />
                        {form.formState.errors.present_address && (
                            <p className="text-red-500 text-sm">{form.formState.errors.present_address.message}</p>
                        )}
                    </div>
                    <div className="grid grid-cols-3 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="present_state">State</Label>
                            <Input {...form.register('present_state')} />
                            {form.formState.errors.present_state && (
                                <p className="text-red-500 text-sm">{form.formState.errors.present_state.message}</p>
                            )}
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="present_district">District</Label>
                            <select
                              {...form.register('present_district')}
                              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
                            >
                              <option value="">Select district</option>
                              {/* @ts-ignore */}
                              {district?.districts?.map((d) => (
                                <option key={d.id} value={d.name}>{d.name}</option>
                              ))}
                            </select>
                            {form.formState.errors.present_district && (
                                <p className="text-red-500 text-sm">{form.formState.errors.present_district.message}</p>
                            )}
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="present_pincode">Pincode</Label>
                            <Input 
                                type="number" 
                                {...form.register('present_pincode', { valueAsNumber: true })} 
                            />
                            {form.formState.errors.present_pincode && (
                                <p className="text-red-500 text-sm">{form.formState.errors.present_pincode.message}</p>
                            )}
                        </div>
                    </div>
                </div>

                <div className="flex items-center mb-2">
                    <input
                        type="checkbox"
                        id="sameAsPresent"
                        className="mr-2"
                        onChange={e => {
                            if (e.target.checked) {
                                const values = form.getValues();
                                form.setValue('permanent_address', values.present_address);
                                form.setValue('permanent_pincode', values.present_pincode);
                                form.setValue('parmanent_state', values.present_state);
                                form.setValue('parmanent_district', values.present_district);
                            }
                        }}
                    />
                    <Label htmlFor="sameAsPresent">Same as present address</Label>
                </div>

                <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Permanent Address</h3>
                    <div className="space-y-2">
                        <Label htmlFor="permanent_address">Address</Label>
                        <textarea
                            {...form.register('permanent_address')}
                            className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
                            rows={4}
                        />
                        {form.formState.errors.permanent_address && (
                            <p className="text-red-500 text-sm">{form.formState.errors.permanent_address.message}</p>
                        )}
                    </div>
                    <div className="grid grid-cols-3 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="parmanent_state">State</Label>
                            <Input {...form.register('parmanent_state')} />
                            {form.formState.errors.parmanent_state && (
                                <p className="text-red-500 text-sm">{form.formState.errors.parmanent_state.message}</p>
                            )}
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="parmanent_district">District</Label>
                            <select
                              {...form.register('parmanent_district')}
                              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
                            >
                              <option value="">Select district</option>
                              {/* @ts-ignore */}
                              {district?.districts.map((d) => (
                                <option key={d.id} value={d.name}>{d.name}</option>
                              ))}
                            </select>
                            {form.formState.errors.parmanent_district && (
                                <p className="text-red-500 text-sm">{form.formState.errors.parmanent_district.message}</p>
                            )}
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="permanent_pincode">Pincode</Label>
                            <Input 
                                type="number" 
                                {...form.register('permanent_pincode', { valueAsNumber: true })} 
                            />
                            {form.formState.errors.permanent_pincode && (
                                <p className="text-red-500 text-sm">{form.formState.errors.permanent_pincode.message}</p>
                            )}
                        </div>
                    </div>
                </div>
            </div>
            <div className="flex gap-4">
                {onBack && <Button type="button" variant="outline" onClick={onBack}>Back</Button>}
                <Button type="submit" className="flex-1">Next</Button>
            </div>
        </form>
    )
} 