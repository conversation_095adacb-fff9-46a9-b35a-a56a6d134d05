import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useExperienceDetails } from '@/hooks/useExperienceDetails'
import { toast } from 'sonner'

const workExperienceSchema = z.object({
  experiences: z.array(
    z.object({
      organisation: z.string().min(1, 'Organisation is required'),
      designation: z.string().min(1, 'Designation is required'),
      from_date: z.string().min(1, 'From date is required'),
      to_date: z.string().min(1, 'To date is required'),
      status: z.string().default('saved'),
    })
  )
})

interface WorkExperienceFormProps {
  onSubmit: (data: any) => void
  onBack?: () => void
  onNext?: () => void
}

//onSubmit
export function WorkExperienceForm({onBack, onNext }: WorkExperienceFormProps) {
  const form = useForm({
    resolver: zodResolver(workExperienceSchema),
    defaultValues: {
      experiences: [
        {
          organisation: '',
          designation: '',
          from_date: '',
          to_date: '',
          status: 'saved',
        },
      ],
    },
    
  })

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'experiences',
  })

  const experienceMutation = useExperienceDetails();

  const handleNext = async (data: any) => {
    console.log(form.formState.errors)
    try {
      for (const exp of data.experiences) {
        const result = await experienceMutation.mutateAsync(exp)
        if (result.success) {
          toast.success('Experiece details saved successfully')
        } else {
          toast.error('Failed to save Experience details')
        }
      }
      onNext?.()
    } catch (error) {
      toast.error('Failed to save education details')
    }
  }



  return (
    <form onSubmit={form.handleSubmit(handleNext)} className="space-y-6">
      <h2 className="text-2xl font-bold mb-6">Work Experience</h2>
      <div className="space-y-8">
        {fields.map((field, idx) => (
          <fieldset key={field.id} className="border rounded-lg p-4 relative mb-4">
            <legend className="font-semibold text-lg mb-2">Experience {idx + 1}</legend>
            {fields.length > 1 && (
              <button
                type="button"
                onClick={() => remove(idx)}
                className="absolute top-2 right-2 text-red-500 hover:text-red-700 text-sm"
              >
                Remove
              </button>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor={`experiences.${idx}.organisation`}>Organisation</Label>
                <Input {...form.register(`experiences.${idx}.organisation`)} />
                {form.formState.errors.experiences?.[idx]?.organisation && (
                  <p className="text-red-500 text-sm">{form.formState.errors.experiences[idx]?.organisation?.message as string}</p>
                )}
              </div>
              <div>
                <Label htmlFor={`experiences.${idx}.designation`}>Designation</Label>
                <Input {...form.register(`experiences.${idx}.designation`)} />
                {form.formState.errors.experiences?.[idx]?.designation && (
                  <p className="text-red-500 text-sm">{form.formState.errors.experiences[idx]?.designation?.message as string}</p>
                )}
              </div>
              <div>
                <Label htmlFor={`experiences.${idx}.from_date`}>From Date</Label>
                <Input type="date" {...form.register(`experiences.${idx}.from_date`)} />
                {form.formState.errors.experiences?.[idx]?.from_date && (
                  <p className="text-red-500 text-sm">{form.formState.errors.experiences[idx]?.from_date?.message as string}</p>
                )}
              </div>
              <div>
                <Label htmlFor={`experiences.${idx}.to_date`}>To Date</Label>
                <Input type="date" {...form.register(`experiences.${idx}.to_date`)} />
                {form.formState.errors.experiences?.[idx]?.to_date && (
                  <p className="text-red-500 text-sm">{form.formState.errors.experiences[idx]?.to_date?.message as string}</p>
                )}
              </div>
            </div>
          </fieldset>
        ))}
        <Button
          type="button"
          variant="outline"
          onClick={() =>
            append({
              organisation: '',
              designation: '',
              from_date: '',
              to_date: '',
              status: 'saved',
            })
          }
        >
          Add Experience
        </Button>
      </div>
      <div className="flex gap-4">
        {onBack && <Button type="button" variant="outline" onClick={onBack}>Back</Button>}
        <Button type="submit" className="flex-1" >Next</Button>
      </div>
    </form>
  )
} 