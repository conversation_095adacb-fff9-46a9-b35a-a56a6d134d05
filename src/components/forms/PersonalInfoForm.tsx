import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

const personalInfoSchema = z.object({
    fathers_name: z.string().min(1, 'Father\'s name is required'),
    mothers_name: z.string().min(1, 'Mother\'s name is required'),
    marital_status: z.string().min(1, 'Marital status is required'),
    status: z.string().default('notsaved')
})

interface PersonalInfoFormProps {
    onSubmit: (data: any) => void
    onBack?: () => void
}

export function PersonalInfoForm({ onSubmit, onBack }: PersonalInfoFormProps) {
    const form = useForm({
        resolver: zodResolver(personalInfoSchema),
        defaultValues: {
            fathers_name: '',
            mothers_name: '',
            marital_status: '',
            status: 'notsaved'
        }
    })

    return (
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <h2 className="text-2xl font-bold mb-6">Personal Information</h2>
            <div className="space-y-4">
                <div className="space-y-2">
                    <Label htmlFor="fathers_name">Father's Name</Label>
                    <Input {...form.register('fathers_name')} />
                    {form.formState.errors.fathers_name && (
                        <p className="text-red-500 text-sm">{form.formState.errors.fathers_name.message}</p>
                    )}
                </div>
                <div className="space-y-2">
                    <Label htmlFor="mothers_name">Mother's Name</Label>
                    <Input {...form.register('mothers_name')} />
                    {form.formState.errors.mothers_name && (
                        <p className="text-red-500 text-sm">{form.formState.errors.mothers_name.message}</p>
                    )}
                </div>
                <div className="space-y-2">
                    <Label htmlFor="marital_status">Marital Status</Label>
                    <select
                        {...form.register('marital_status')}
                        className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
                    >
                        <option value="">Select marital status</option>
                        <option value="Single">Single</option>
                        <option value="Married">Married</option>
                        <option value="Divorced">Divorced</option>
                        <option value="Widowed">Widowed</option>
                    </select>
                    {form.formState.errors.marital_status && (
                        <p className="text-red-500 text-sm">{form.formState.errors.marital_status.message}</p>
                    )}
                </div>
            </div>
            <div className="flex gap-4">
                {onBack && <Button type="button" variant="outline" onClick={onBack}>Back</Button>}
                <Button type="submit" className="flex-1">Next</Button>
            </div>
        </form>
    )
} 