import { useNavigate } from 'react-router-dom';
import { useStoreUserData } from '../store/useStoreUserData';
import { useApplicationStore } from '../store/applicationStore';
import { isAuthenticated } from '../store/isAuthenticated';
import { Button } from './ui/button';
import  Logo from '../assets/aslrm.jpeg'

export function Navbar() {
    const navigate = useNavigate(); 
    const clearUserData = useStoreUserData((state) => state.clearUserData);
    const clearApplicationId = useApplicationStore((state) => state.clearApplicationId);

    const handleLogout = () => {
        localStorage.clear();
        clearUserData();
        clearApplicationId();
        navigate('/');
    };

    return (
<nav className="bg-white border-gray-200 dark:bg-gray-900">
  <div className="max-w-screen-xl flex flex-wrap items-center justify-between mx-auto p-4">
  <a href="/" className="flex items-center space-x-3 rtl:space-x-reverse">
      <img src={Logo} className="h-25 w-full" alt="" />
   
  </a>
  <div className="flex md:order-2 space-x-3 md:space-x-0 rtl:space-x-reverse">
     
      <button data-collapse-toggle="navbar-cta" type="button" className="inline-flex items-center p-2 w-10 h-10 justify-center text-sm text-gray-500 rounded-lg md:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-600" aria-controls="navbar-cta" aria-expanded="false">
        <span className="sr-only">Open main menu</span>
        <svg className="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 17 14">
            <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M1 1h15M1 7h15M1 13h15"/>
        </svg>
    </button>
    {isAuthenticated() && (
      <Button onClick={() => navigate('/applicationhistory')} variant="outline" className="ml-4">Application History</Button> 
    )}
    {isAuthenticated() && (
      <Button onClick={handleLogout} variant="destructive" className="ml-4">Logout</Button>
    )}
  </div>
  <div className="items-center justify-between hidden w-full md:flex md:w-auto md:order-1" id="navbar-cta">
    <ul className="flex flex-col font-medium p-4 md:p-0 mt-4 border border-gray-100 rounded-lg bg-gray-50 md:space-x-8 rtl:space-x-reverse md:flex-row md:mt-0 md:border-0 md:bg-white dark:bg-gray-800 md:dark:bg-gray-900 dark:border-gray-700">
       
    </ul>
  </div>
  </div> 
  <div className="border-b" />
</nav>
    )
}