import { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { useJobs } from "@/hooks/useJobs"
import { useCreateApplication } from "@/hooks/useCreateApplication"
import { toast } from "sonner"
import { <PERSON><PERSON><PERSON>ir<PERSON>, Loader2 } from "lucide-react"

interface User {
    id: number
    first_name: string
    last_name: string
    email: string
    phoneNumber: string
    Qualification: string
    user_application_id :any,

}

export default function JobList() {
    const navigate = useNavigate()
    const { data: jobs, isLoading, error } = useJobs()
    const createApplication = useCreateApplication()
    const [appliedJobs,] = useState(new Set())
    const [displayData, setDisplayData] = useState<User | null>(null)

    useEffect(() => {
        const storedUser = localStorage.getItem('user')
        if (storedUser) {
            setDisplayData(JSON.parse(storedUser))
        }
    }, [])

    const handleApply = async (jobId: number) => {
        if (!displayData?.id) {
            toast.error('User data not found. Please login again.')
            return
        }

        try {
            const result = await createApplication.mutateAsync({
                user_id: displayData.id,
                job_id: jobId
            })

            if (result.success) {
                toast.success('Application in progress....')
                navigate('/applicationform')
            }
        } catch (error) {
            toast.error('Failed to submit application ! Please verify last apply date and Proceed ')
        }
    }

    if (isLoading) {
        return (
            <div className="min-h-screen bg-gray-50 py-8">
                <div className="mx-auto max-w-7xl px-4">
                    <div className="text-center">
                        <Loader2 className="w-10 h-10 animate-spin text-blue-600" />
                    </div>
                </div>
            </div>
        )
    }

    if (error) {
        return (
            <div className="min-h-screen bg-gray-50 py-8">
                <div className="mx-auto max-w-7xl px-4">
                    <div className="text-center text-red-600">
                        <AlertCircle className="w-10 h-10 text-red-600" />
                    </div>
                </div>
            </div>
        )
    }

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="mx-auto max-w-7xl px-4">
                {/* User Profile Section */}
                {displayData && (
                    <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">

                        <h2 className="text-xl font-semibold mb-4">Welcome        <div className="text-red-700"> {displayData.user_application_id} </div> </h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p className="text-gray-600">Name: <span className="text-gray-900">{displayData.first_name} {displayData.last_name}</span></p>
                                <p className="text-gray-600">Email: <span className="text-gray-900">{displayData.email}</span></p>
                            </div>  
                            <div>
                                <p className="text-gray-600">Phone: <span className="text-gray-900">{displayData.phoneNumber}</span></p>
                                <p className="text-gray-600">Qualification: <span className="text-gray-900">{displayData.Qualification}</span></p>
                            </div>
                        </div>
                    </div>
                )}

                {/* Jobs List */}
                <div className="grid grid-cols-1 gap-6">
                    {jobs?.map((job) => (
                        <div key={job.id} className="bg-white rounded-lg shadow-sm border p-6">
                            <div className="flex justify-between items-start">
                                <div>
                                    <h3 className="text-xl font-semibold text-gray-900">{job.project}</h3>
                                    <p className="text-gray-600 mt-1">{job.position}</p>
                                </div>
                                <button
                                    onClick={() => handleApply(job.id)}
                                    disabled={appliedJobs.has(job.id)}
                                    className={`px-4 py-2 rounded-md text-sm font-medium ${
                                        appliedJobs.has(job.id)
                                            ? 'bg-green-100 text-green-800 cursor-not-allowed'
                                            : 'bg-blue-600 text-white hover:bg-blue-700'
                                    }`}
                                >
                                    {appliedJobs.has(job.id) ? 'Applied' : 'Apply Now'}
                                </button>
                            </div>
                            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                <div>
                                    <p className="text-sm text-gray-500">Qualification</p>
                                    <p className="text-gray-900">{job.qualification}</p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-500">Experience</p>
                                    <p className="text-gray-900">{job.experience}</p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-500">Last Date to Apply</p>
                                    <p className="text-gray-900">{new Date(job.last_date).toLocaleDateString()}</p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-500">Vacancies</p>
                                    <p className="text-gray-900">{job.vacancies}</p>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    )
}