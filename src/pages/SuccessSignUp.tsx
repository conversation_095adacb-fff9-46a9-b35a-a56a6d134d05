import { <PERSON><PERSON><PERSON><PERSON>, Co<PERSON>, <PERSON><PERSON><PERSON>, User, Shield } from 'lucide-react'
import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
export function SuccessSignUp() {
    const navigate = useNavigate()
    const user = localStorage.getItem('User_ID')
    
    const handleNavigateToLogin = () => {
        navigate('/')
    }
    const [copied, setCopied] = useState(false)

    const handleCopyUserID = () => {
        if (user) {
            navigator.clipboard.writeText(user)
            setCopied(true)
            setTimeout(() => setCopied(false), 2000)
        }
    }

    if(!user) {
        navigate('/');
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center p-4">
          <div className="max-w-md w-full">
                {/* Main Content Card */}
                <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
                    {/* Header */}
                    <div className="bg-gradient-to-r from-green-500 to-blue-600 px-8 py-6 text-center">
                        <h1 className="text-2xl font-bold text-white mb-2">
                            Welcome Aboard! 
                        </h1>
                        <p className="text-green-100">
                            Your account has been successfully created
                        </p>
                    </div>

                    {/* Content */}
                    <div className="px-8 py-6 space-y-6">
                        {/* User ID Section */}
                        {user && (
                            <div className="bg-gray-50 rounded-xl p-4 border border-gray-200">
                                <div className="flex items-center gap-2 mb-3">
                                    <User className="h-5 w-5 text-blue-600" />
                                    <h3 className="font-semibold text-gray-900">Your User ID</h3>
                                </div>
                                
                                <div className="flex items-center justify-between bg-white rounded-lg p-3 border border-gray-200">
                                    <code className="font-mono text-sm text-gray-800 break-all">
                                        {user}
                                    </code>
                                    <button
                                        onClick={handleCopyUserID}
                                        className="ml-2 flex-shrink-0 p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors duration-200"
                                        title="Copy User ID"
                                    >
                                        <Copy className="h-4 w-4" />
                                    </button>
                                </div>
                                
                                {copied && (
                                    <p className="text-sm text-green-600 mt-2 flex items-center gap-1">
                                        <CheckCircle className="h-4 w-4" />
                                        User ID copied to clipboard!
                                    </p>
                                )}
                            </div>
                        )}

                        {/* Important Notice */}
                        <div className="bg-amber-50 border border-amber-200 rounded-xl p-4">
                            <div className="flex items-start gap-3">
                                <Shield className="h-5 w-5 text-amber-600 flex-shrink-0 mt-0.5" />
                                <div>
                                    <h4 className="font-semibold text-amber-800 mb-1">
                                        Important: Save Your User ID
                                    </h4>
                                    <p className="text-sm text-amber-700 leading-relaxed">
                                        Please save this User ID in a secure location 
                                        <span className="text-sm text-gray-900 "> use your DOB(ddmmyyyy) as your password.</span>
                                        You'll need it for future reference and account recovery.
                                    </p>
                                </div>
                            </div>
                        </div>

                        {/* Next Steps */}
                        <div className="space-y-3">
                            <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                                <ArrowRight className="h-4 w-4 text-blue-600" />
                                What's Next?
                            </h4>
                            <ul className="space-y-2 text-sm text-gray-600 ml-6">
                                <li className="flex items-start gap-2">
                                    <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></span>
                                    Log in to your account to get started
                                </li>
                              
                            </ul>
                        </div>
                    </div>

                    {/* Footer Actions */}
                    <div className="px-8 py-6 bg-gray-50 border-t border-gray-200">
                        <button
                            onClick={handleNavigateToLogin}
                            className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
                        >
                            Continue to Login
                            <ArrowRight className="h-5 w-5" />
                        </button>
                        
                        <p className="text-center text-xs text-gray-500 mt-4">
                            By continuing, you agree to our Terms of Service and Privacy Policy
                        </p>
                    </div>
                </div>

                {/* Additional Help */}
                <div className="text-center mt-6">
                    <p className="text-sm text-gray-500">
                        Need help? Contact our{' '}
                        <button className="text-blue-600 hover:text-blue-700 font-medium">
                            support team
                        </button>
                    </p>
                </div>
            </div>
        </div>
    )
}