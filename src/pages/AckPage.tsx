import { useApplicationStore } from '@/store/applicationStore'
import { useApplicationDetails } from '@/hooks/useFetchApplicationDetails'
import ApplicationAcknowledgement from '@/components/forms/FormSummary'
import { SplineIcon } from 'lucide-react'

export default function AckPage() {
  const { applicationId } = useApplicationStore()
  const { data, isLoading, isError } = useApplicationDetails(applicationId || '')
   
  if (isLoading) return <div>
    <SplineIcon/>
  </div>
  if (isError || !data) return <div>Error loading application data.</div>

  //@ts-ignore
  return <ApplicationAcknowledgement data={data} />
}
