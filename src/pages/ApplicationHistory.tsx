import { useGetApplicationHistory } from '@/hooks/useGetApplicationHistory'
import { Calendar, MapPin, Users, Building2, Hash } from 'lucide-react'
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { useCreateApplication } from '@/hooks/useCreateApplication';

function ApplicationHistory() {
    const { data: applicationHistory, isLoading: loadingApplicationHistory, isError: errorApplicationHistory } = useGetApplicationHistory()
    const navigate = useNavigate();
    const createApplication = useCreateApplication();

    if (loadingApplicationHistory) {
        return (
            <div className="flex items-center justify-center min-h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
        )
    }
  

    if (errorApplicationHistory) {
        return (
            <div className="flex items-center justify-center min-h-64">
                <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md">
                    <div className="flex items-center">
                        <div className="flex-shrink-0">
                            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="ml-3">
                            <h3 className="text-sm font-medium text-red-800">Error Loading Applications</h3>
                            {/* @ts-ignore */}
                            <p className="text-sm text-red-700 mt-1">{errorApplicationHistory?.message || 'Unable to load application history'}</p>
                        </div>
                    </div>
                </div>
            </div>
        )
    }
    // @ts-ignore
    const applications = applicationHistory?.applications || []

    if (applications.length === 0) {
        return (
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="text-center">
                    <h1 className="text-3xl font-bold text-gray-900 mb-8">Application History</h1>
                    <div className="bg-gray-50 rounded-lg p-12">
                        <Building2 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">No Applications Yet</h3>
                        <p className="text-gray-600">You haven't submitted any job applications yet. Start exploring opportunities!</p>
                    </div>
                </div>
            </div>
        )
    }

    const handleFinish = (job_id, user_id) => {
        createApplication.mutate(
            { job_id, user_id },
            {
                onSuccess: () => {
                    navigate('/applicationform', { state: { job_id, user_id } });
                },
                onError: (error) => {
                    console.error(error);
                }
            }
        );
    };

    return (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900">Application History</h1>
                <p className="text-gray-600 mt-2">Track your job applications and their details</p>
            </div>

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {applications.map((application) => (
                    <div key={application.id} className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                        <div className="p-6">
                            <div className="flex items-start justify-between mb-4">
                                <div className="flex-1">
                                    <h2 className="text-xl font-semibold text-gray-900 mb-2">
                                        {application.job_application.position}
                                    </h2>
                                    <div className="flex items-center text-sm text-gray-600 mb-2">
                                        <MapPin className="h-4 w-4 mr-1" />
                                        <span className="font-mono text-xs text-red-600">Application ID: {application.application_id}</span>
                                    </div>
                                </div>
                                <div className="flex-shrink-0">
                                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {application.job_application.level}
                                    </span>
                                </div>
                            </div>

                            <div className="space-y-3">
                                <div className="flex items-center justify-between text-sm">
                                    <div className="flex items-center text-gray-600">
                                        <Calendar className="h-4 w-4 mr-2" />
                                        <span>Age Range:</span>
                                    </div>
                                    <span className="font-medium text-gray-900">
                                        {application.job_application.min_age} - {application.job_application.max_age} years
                                    </span>
                                </div>

                                <div className="flex items-center justify-between text-sm">
                                    <div className="flex items-center text-gray-600">
                                        <Building2 className="h-4 w-4 mr-2" />
                                        <span>Group:</span>
                                    </div>
                                    <span className="font-medium text-gray-900">
                                        {application.job_application.group}
                                    </span>
                                </div>

                                <div className="flex items-center justify-between text-sm">
                                    <div className="flex items-center text-gray-600">
                                        <Users className="h-4 w-4 mr-2" />
                                        <span>Vacancies:</span>
                                    </div>
                                    <span className="font-medium text-gray-900">
                                        {application.job_application.vacancies}
                                    </span>
                                </div>

                                <div className="flex items-center justify-between text-sm">
                                    <div className="flex items-center text-gray-600">
                                        <Hash className="h-4 w-4 mr-2" />
                                        <span>Application ID:</span>
                                    </div>
                                    <span className="font-medium text-gray-900 font-mono text-xs">
                                        {application.id}
                                    </span>
                                </div>
                                <div>
                                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {application.status}
                                    </span>
                                </div>
                            </div>

                            {application.status === 'pending' && (
                                <div className="mt-4 text-center">
                                    <Button onClick={() => handleFinish(application.job_id, application.user_id)}>
                                        Finish Now
                                    </Button>
                                </div>
                            )}
                        </div>
                    </div>
                ))}
            </div>

            <div className="mt-8 text-center">
                <p className="text-sm text-gray-500">
                    Showing {applications.length} application{applications.length !== 1 ? 's' : ''}
                </p>
            </div>
        </div>
    )
}

export default ApplicationHistory