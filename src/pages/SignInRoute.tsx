import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { SignIn } from './SignIn';

export function SignInRoute() {
  const navigate = useNavigate();
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      navigate('/joblisting', { replace: true });
    } else {
      localStorage.removeItem('token');
    }
  }, [navigate]);
  return !localStorage.getItem('token') ? <SignIn /> : null;
} 