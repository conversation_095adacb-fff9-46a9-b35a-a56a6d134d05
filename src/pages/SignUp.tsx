import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuItem, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { toast } from "sonner"
import { useNavigate } from "react-router-dom"
import { useRegisterUser } from "@/hooks/useRegisterUser"



const signUpSchema = z.object({
    email: z.string().email("Invalid email address"),
    first_name: z.string().min(2, "First name must be at least 2 characters"),
    middle_name: z.string().optional(),
    last_name: z.string().min(2, "Last name must be at least 2 characters"),
    role: z.string(),
    dob: z.string().min(1, "Date of birth is required"),
    phoneNumber: z.string().min(10, "Phone number must be at least 10 characters"),
    Qualification: z.string().min(1, "Qualification is required"),
    caste: z.string().min(1, "Caste is required"),
    pwbd_status: z.string().min(1, "PWBD status is required"),
    gender: z.string().min(1, "Gender is required"),
    password: z.string().min(5, "Password must be at least 5 characters"),
})

type SignUpForm = z.infer<typeof signUpSchema>

export function SignUp() {
    
    const navigate = useNavigate()
    const { mutate: registerUser } = useRegisterUser()
    const {
        register,
        handleSubmit,
        setValue,
        watch,
        formState: { errors }
    } = useForm<SignUpForm>({
        resolver: zodResolver(signUpSchema),
        defaultValues: {
            role: "user"
        }
    })

    const onSubmit = (data: SignUpForm) => {
        registerUser({
            url: `${import.meta.env.VITE_API_URL}/api/auth/register`,
            body: data
        }, {
            onSuccess: (response) => {
                toast.success("Sign up successful")
                localStorage.setItem('User_ID', response.newUser.user_application_id);
                navigate('/successsignup')
            },
            onError: (error) => {
                toast.error("Sign up failed: " + error.message)
            }
        })
    }

    return (
        <div className="mt-[100px] mx-auto max-w-[1000px]">
            <Card>
                <CardHeader>
                    <CardTitle>Sign Up</CardTitle>
                </CardHeader>
                <CardContent>
                    <form onSubmit={handleSubmit(onSubmit)}>
                        <div className="grid gap-6 mb-6 md:grid-cols-2">
                            <div className="space-y-2">
                                <Label htmlFor="email">Email</Label>
                                <Input 
                                    {...register("email")}
                                    type="email" 
                                    id="email" 
                                    placeholder="<EMAIL>" 
                                />
                                {errors.email && (
                                    <p className="text-sm text-red-500">{errors.email.message}</p>
                                )}
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="first_name">First name</Label>
                                <Input 
                                    {...register("first_name")}
                                    type="text" 
                                    id="first_name" 
                                    placeholder="John" 
                                />
                                {errors.first_name && (
                                    <p className="text-sm text-red-500">{errors.first_name.message}</p>
                                )}
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="middle_name">Middle name</Label>
                                <Input 
                                    {...register("middle_name")}
                                    type="text" 
                                    id="middle_name" 
                                    placeholder="D" 
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="last_name">Last name</Label>
                                <Input 
                                    {...register("last_name")}
                                    type="text" 
                                    id="last_name" 
                                    placeholder="Doe" 
                                />
                                {errors.last_name && (
                                    <p className="text-sm text-red-500">{errors.last_name.message}</p>
                                )}
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="dob">Date of Birth</Label>
                                <Input 
                                    {...register("dob")}
                                    type="date" 
                                    id="dob" 
                                />
                                {errors.dob && (
                                    <p className="text-sm text-red-500">{errors.dob.message}</p>
                                )}
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="phoneNumber">Phone number</Label>
                                <Input 
                                    {...register("phoneNumber")}
                                    type="tel" 
                                    id="phoneNumber" 
                                    placeholder="1234567890" 
                                />
                                {errors.phoneNumber && (
                                    <p className="text-sm text-red-500">{errors.phoneNumber.message}</p>
                                )}
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="caste">Caste</Label>
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Input
                                            type="text"
                                            id="caste"
                                            placeholder="Select caste"
                                            value={watch("caste") || ""}
                                            readOnly
                                            className="cursor-pointer"
                                        />
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent>   
                                        {['General','ST(H)','ST(P)','SC','OBC/MOBC'].map((c) => (
                                            <DropdownMenuItem
                                                key={c}
                                                onClick={() => setValue("caste", c)}
                                            >
                                                {c}
                                            </DropdownMenuItem>
                                        ))}
                                    </DropdownMenuContent>
                                </DropdownMenu>
                                {errors.caste && (
                                    <p className="text-sm text-red-500">{errors.caste.message}</p>
                                )}
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="Qualification">Qualification</Label>
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Input
                                            type="text"
                                            id="Qualification"
                                            placeholder="Select qualification"
                                            value={watch("Qualification") || ""}
                                            readOnly
                                            className="cursor-pointer"
                                        />
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent>
                                        {['class 10','class 12','Graduation','Masters','Phd'].map((q) => (
                                            <DropdownMenuItem
                                                key={q}
                                                onClick={() => setValue("Qualification", q)}
                                            >
                                                {q}
                                            </DropdownMenuItem>
                                        ))}
                                    </DropdownMenuContent>
                                </DropdownMenu>
                                {errors.Qualification && (
                                    <p className="text-sm text-red-500">{errors.Qualification.message}</p>
                                )}
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="pwbd_status">PWBD Status</Label>
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Input
                                            type="text"
                                            id="pwbd_status"
                                            placeholder="Select status"
                                            value={watch("pwbd_status") || ""}
                                            readOnly
                                            className="cursor-pointer"
                                        />
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent>
                                        {["active", "inactive"].map((status) => (
                                            <DropdownMenuItem
                                                key={status}
                                                onClick={() => setValue("pwbd_status", status)}
                                            >
                                                {status}
                                            </DropdownMenuItem>
                                        ))}
                                    </DropdownMenuContent>
                                </DropdownMenu>
                                {errors.pwbd_status && (
                                    <p className="text-sm text-red-500">{errors.pwbd_status.message}</p>
                                )}
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="gender">Gender</Label>
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Input
                                            type="text"
                                            id="gender"
                                            placeholder="Select gender"
                                            value={watch("gender") || ""}
                                            readOnly
                                            className="cursor-pointer"
                                        />
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent>
                                        {["male", "female"].map((g) => (
                                            <DropdownMenuItem
                                                key={g}
                                                onClick={() => setValue("gender", g)}
                                            >
                                                {g.charAt(0).toUpperCase() + g.slice(1)}
                                            </DropdownMenuItem>
                                        ))}
                                    </DropdownMenuContent>
                                </DropdownMenu>
                                {errors.gender && (
                                    <p className="text-sm text-red-500">{errors.gender.message}</p>
                                )}
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="password">Password</Label>
                                <Input 
                                    {...register("password")}
                                    type="password" 
                                    id="password" 
                                    placeholder="Enter your DOB(ddmmyyyy)" 
                                />
                                {errors.password && (
                                    <p className="text-sm text-red-500">{errors.password.message}</p>
                                )}
                            </div>
                        </div>
                        <Button type="submit" className="w-[100px] mx-auto align-center">Submit</Button>
                    </form>
                </CardContent>
            </Card>
        </div>
    )
}