import React from 'react';
import { Home, ArrowLeft, Search, AlertTriangle } from 'lucide-react';

interface RouteNotFoundProps {
  onGoHome?: () => void;
  onGoBack?: () => void;
  onSearch?: (query: string) => void;

  supportEmail?: string;
}

const RouteNotFound: React.FC<RouteNotFoundProps> = ({
  onGoHome = () => window.location.href = '/',
  onGoBack = () => window.history.back(),
  onSearch,
 
  supportEmail = '<EMAIL>'
}) => {
  const [searchQuery, setSearchQuery] = React.useState('');
  const [isAnimated, setIsAnimated] = React.useState(false);

  React.useEffect(() => {
    setIsAnimated(true);
  }, []);

  const handleSearch = () => {
    if (onSearch && searchQuery.trim()) {
      onSearch(searchQuery.trim());
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
        <div className="absolute top-40 left-1/2 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-500"></div>
      </div>

      <div className={`relative max-w-2xl mx-auto text-center transform transition-all duration-1000 ${
        isAnimated ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
      }`}>
        {/* 404 Icon */}
        <div className="mb-8 relative">
          <div className="inline-flex items-center justify-center w-32 h-32 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full shadow-2xl">
            <AlertTriangle className="w-16 h-16 text-white animate-bounce" />
          </div>
          <div className="absolute inset-0 w-32 h-32 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-lg opacity-50 mx-auto"></div>
        </div>

        {/* Main heading */}
        <h1 className="text-8xl md:text-9xl font-black text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 mb-4 animate-pulse">
          404
        </h1>

        <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
          Oops! Page Not Found
        </h2>

        <p className="text-xl text-gray-300 mb-12 max-w-lg mx-auto leading-relaxed">
          The page you're looking for seems to have vanished into the digital void. 
          Don't worry, even the best explorers sometimes take a wrong turn.
        </p>

        {/* Search bar */}
        {onSearch && (
          <div className="mb-8 max-w-md mx-auto">
            <div className="relative">
              <input
                type="text"
                placeholder="Search for what you need..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="w-full px-6 py-4 pl-12 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
              />
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-300" />
              <button
                onClick={handleSearch}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-2 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 hover:scale-105"
              >
                Search
              </button>
            </div>
          </div>
        )}

        {/* Action buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
          <button
            onClick={onGoHome}
            className="group flex items-center space-x-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-4 rounded-2xl font-semibold shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
          >
            <Home className="w-5 h-5 group-hover:rotate-12 transition-transform duration-300" />
            <span>Go Home</span>
          </button>

          <button
            onClick={onGoBack}
            className="group flex items-center space-x-3 bg-white/10 backdrop-blur-md border border-white/20 hover:bg-white/20 text-white px-8 py-4 rounded-2xl font-semibold shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
          >
            <ArrowLeft className="w-5 h-5 group-hover:-translate-x-1 transition-transform duration-300" />
            <span>Go Back</span>
          </button>
        </div>

        {/* Help section */}
        <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-6 max-w-md mx-auto">
          <h3 className="text-lg font-semibold text-white mb-2">Need Help?</h3>
          <p className="text-gray-300 text-sm mb-4">
            If you believe this is an error, please contact our support team.
          </p>
          <a
            href={`mailto:${supportEmail}`}
            className="text-purple-400 hover:text-purple-300 font-medium transition-colors duration-300"
          >
            {supportEmail}
          </a>
        </div>

        {/* Footer */}
        <div className="mt-12 text-gray-400 text-sm">
          <p>Error Code: 404 | Page Not Found</p>
        </div>
      </div>
    </div>
  );
};

export default RouteNotFound;