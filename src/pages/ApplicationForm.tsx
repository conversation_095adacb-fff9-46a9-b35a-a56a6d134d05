import { useState, useEffect } from 'react'
import { toast } from 'sonner'
import { useApplicationStore } from '@/store/applicationStore'
import { usePersonalDetails } from '@/hooks/usePersonalDetails'
import { useEducationalDetails } from '@/hooks/useEducationalDetails'
import { useExperienceDetails } from '@/hooks/useExperienceDetails'
import { PersonalInfoForm } from '@/components/forms/PersonalInfoForm'
import { EducationalInfoForm } from '@/components/forms/EducationalInfoForm'
import { WorkExperienceForm } from '@/components/forms/WorkExperienceForm'
import { AdditionalInfoForm } from '@/components/forms/AdditionalInfoForm'
import { DocumentUploadForm } from '@/components/forms/DocumentUploadForm'
import { useApplicationDetails } from '@/hooks/useFetchApplicationDetails'
import { Info } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import AckPage from './AckPage'


export default function ApplicationForm() {



 
    const [currentStep, setCurrentStep] = useState(1)
    const [, setFormData] = useState({})
    const [, setUserData] = useState<any>(null)
     const navigate = useNavigate()

    const { applicationId } = useApplicationStore()
    const createPersonalDetails = usePersonalDetails()
    const createEducationalDetails = useEducationalDetails()
    const createExperienceDetails = useExperienceDetails()
    const application_id = applicationId?.toString() || ''

    const { data: applicationDetails, isLoading: loadingOverview, isError } = useApplicationDetails(application_id)
    useEffect(() => {
        const storedUser = localStorage.getItem('user')
        if (storedUser) {
            setUserData(JSON.parse(storedUser))
        }
    }, [])

    console.log("By Id",applicationDetails)

    const onSubmitPersonalInfo = async (data: any) => {
        try {
            const result = await createPersonalDetails.mutateAsync(data)
            if (result.success) {
                toast.success('Personal details saved successfully')
                setFormData(prev => ({ ...prev, ...data }))
                setCurrentStep(2)
            }
        } catch (error) {
            toast.error('Failed to save personal details')
        }
    }

    const onSubmitEducationalInfo = async (data: any) => {
        try {
            console.log(data.education)
            const result = await createEducationalDetails.mutateAsync(data)
            if (result.success) {
                toast.success('Educational details saved successfully')
                setFormData(prev => ({ ...prev, ...data }))
                setCurrentStep(3)
            }
        } catch (error) {
            toast.error('Failed to save educational details')
        }
    }

    const onSubmitWorkExperience = async (data: any) => {
        try {
            const result = await createExperienceDetails.mutateAsync(data)
            if (result.success) {
                toast.success('Experience details saved successfully')
                setFormData(prev => ({ ...prev, ...data }))
                setCurrentStep(4)
            }
        } catch (error) {
            toast.error('Failed to save experience details')
        }
    }

    // Step 4: Save address info and go to upload step
    const onSubmitAdditionalInfo = async (data: any) => {
        try {
            setFormData(prev => ({ ...prev, ...data }))
            setCurrentStep(5)
        } catch (error) {
            toast.error('Failed to save additional info')
        }
    }

 

    const renderStep = () => {
        switch (currentStep) {
            case 1:
                return <PersonalInfoForm onSubmit={onSubmitPersonalInfo} />
            case 2:
                return <AdditionalInfoForm onSubmit={onSubmitAdditionalInfo} onBack={() => setCurrentStep(1)} applicationId={application_id} onNext={() => setCurrentStep(3)} />
            case 3:
                return <EducationalInfoForm onSubmit={onSubmitEducationalInfo} onBack={() => setCurrentStep(2)}onNext={() => setCurrentStep(4)} />
            case 4:
                return <WorkExperienceForm onSubmit={onSubmitWorkExperience} onBack={() => setCurrentStep(3)} onNext={() => setCurrentStep(5)} />
            case 5:
                return (
                    <DocumentUploadForm
                        applicationId={application_id}
                        onBack={() => setCurrentStep(4)}
                        onSuccess={() => navigate('/ack')}
                    />
                )
            case 6:
                if (loadingOverview) {
                    return <div>Loading application overview...</div>
                }
                if (isError || !applicationDetails || !applicationDetails.application) {
                    return <div>No application data found.</div>
                }
                return (
                    <div>
                        <AckPage />
                    </div>
                )
            default:
                return null
        }
    }

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="mx-auto max-w-2xl px-4">
                <div className="bg-white rounded-lg shadow-sm border p-6">
                    <div className="mb-8">
                    <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Application Form
          </h1>
          <p className="text-sm text-gray-600 leading-relaxed">
            Complete all sections to submit your application
          </p>
        </div>
        <div className="flex items-center space-x-2 bg-blue-50 px-3 py-1 rounded-full">
          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
          <span className="text-sm font-medium text-blue-700">
            Step {currentStep} of 6
          </span>
        </div>
      </div>

      {/* Instructions panel */}
      <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
        <div className="flex items-start space-x-3">
          <Info className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
          <div className="space-y-2">
            <h3 className="font-semibold text-gray-900 text-sm">
              Before you begin:
            </h3>
            <ul className="text-sm text-gray-700 space-y-1">
              <li className="flex items-start">
                <span className="text-red-500 mr-2">•</span>
                Fill all required (*) fields to proceed to the next step
              </li>
              <li className="flex items-start">
                <span className="text-blue-500 mr-2">•</span>
                Use correct formats — Date: DD/MM/YYYY, Phone: 10 digits, Email: valid address
              </li>
              <li className="flex items-start">
                <span className="text-green-500 mr-2">•</span>
                If present and permanent addresses are the same, tick the checkbox to auto-fill
              </li>
              <li className="flex items-start">
                <span className="text-orange-500 mr-2">•</span>
                Review all steps before clicking Submit — changes cannot be made after submission
              </li>
            </ul>
          </div>
        </div>
      </div>
                    </div>
                    {renderStep()}
                </div>
            </div>
        </div>
    )
}