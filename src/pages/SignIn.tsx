import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardAction,
  CardContent,
  CardDescription,

  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useNavigate } from "react-router-dom"
import { toast } from "sonner"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { useLogin } from "@/hooks/useLogin"
import { useStoreUserData } from "@/store/useStoreUserData"

const loginSchema = z.object({
  user_application_id: z.string().min(1, "ID is required"),
  password: z.string().min(1, "Password is required"),
});

type LoginForm = z.infer<typeof loginSchema>;

export function SignIn() {
  const navigate = useNavigate();
  const { mutate: login } = useLogin();
  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<LoginForm>({
    resolver: zodResolver(loginSchema)
  });

  const onSubmit = (data: LoginForm) => {
    login(data, {
      onSuccess: (response) => {
          // Store the token in localStorage
        useStoreUserData.setState({
          userData: {
            accessToken: response.accessToken,
            //@ts-ignore
            user: response.user ,
            Registration_Number : response.user_application_id
          }
        });
        localStorage.setItem('token', response.accessToken);
        localStorage.setItem('user', JSON.stringify(response.user));
        console.log(useStoreUserData.getState().userData);
        toast.success("Login successful");
        navigate('/joblisting');
      },
      onError: (error) => {
        toast.error("Login failed: " + error.message);
      }
    });
  };

  return (
    <Card className="w-full max-w-sm mx-auto mt-[100px]">
      <CardHeader>
        <CardTitle>Login to your account</CardTitle>
        <CardDescription>
          Enter your email below to login to your account
        </CardDescription>
        <CardAction>
          <Button variant="link" onClick={() => navigate('/signup')}>Sign Up</Button>
        </CardAction>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="flex flex-col gap-6">
            <div className="grid gap-2">
              <Label htmlFor="id">ID</Label>
              <Input
                {...register("user_application_id")}
                type="text"
                placeholder="**********"
                required
              />
              {errors.user_application_id && (
                <p className="text-sm text-red-500">{errors.user_application_id.message}</p>
              )}
            </div>
            <div className="grid gap-2">
              <div className="flex items-center">
                <Label htmlFor="password">Password</Label>
                <a
                  href="#"
                  className="ml-auto inline-block text-sm underline-offset-4 hover:underline"
                >
                   
                </a>
              </div>
              <Input 
                {...register("password")}
                type="password" 
                required 
              />
              {errors.password && (
                <p className="text-sm text-red-500">{errors.password.message}</p>
              )}
            </div>
          </div>
          <Button type="submit" className="w-full mt-6">
            Login
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
